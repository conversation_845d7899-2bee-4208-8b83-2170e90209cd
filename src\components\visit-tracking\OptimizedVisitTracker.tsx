import React, { useState, useCallback, useMemo, Suspense, lazy } from 'react';
import { useNavigate } from 'react-router-dom';
import { useData } from '@/context/data';
import { useAuth } from '@/context/auth/AuthContext';
import { VisitStatus, HouseType } from '@/types';
import { toast } from 'sonner';
import { Card, CardContent, AddressCard } from '@/design-system/components/Card';
import { Button, QuickActionButton } from '@/design-system/components/Button';
import { useSwipeNavigation, triggerHapticFeedback } from '@/hooks/useSwipeGestures';
import { DollarSign, Calendar, X, Clock, ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';

// Lazy load heavy components
const AppointmentDialog = lazy(() => import('../visit/AppointmentDialog'));
const ProductSelection = lazy(() => import('../product/ProductSelection'));

interface OptimizedVisitTrackerProps {
  visitId?: string;
  initialData?: {
    street: string;
    houseNumber: string;
    city: string;
    zipCode: string;
    houseType: HouseType;
  };
  onComplete?: () => void;
}

// Memoized loading component
const LoadingSpinner = React.memo(() => (
  <div className="flex items-center justify-center p-8">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
  </div>
));

// Memoized step indicator
const StepIndicator = React.memo<{ currentStep: number; totalSteps: number }>(
  ({ currentStep, totalSteps }) => (
    <div className="flex justify-center space-x-2 mb-6">
      {Array.from({ length: totalSteps }, (_, i) => (
        <div
          key={i}
          className={cn(
            'w-3 h-3 rounded-full transition-all duration-250',
            i + 1 === currentStep ? 'bg-blue-600 scale-110' : 'bg-neutral-300'
          )}
        />
      ))}
    </div>
  )
);

// Memoized quick action grid
const QuickActionGrid = React.memo<{
  onAction: (status: VisitStatus) => void;
  isLoading: boolean;
}>(({ onAction, isLoading }) => {
  const actions = useMemo(() => [
    {
      status: 'Angetroffen → Sale' as VisitStatus,
      icon: <DollarSign className="h-8 w-8" />,
      label: 'Verkauf!',
      variant: 'sale' as const,
      color: 'bg-green-600'
    },
    {
      status: 'Angetroffen → Termin' as VisitStatus,
      icon: <Calendar className="h-8 w-8" />,
      label: 'Termin',
      variant: 'appointment' as const,
      color: 'bg-blue-600'
    },
    {
      status: 'Angetroffen → Kein Interesse' as VisitStatus,
      icon: <X className="h-8 w-8" />,
      label: 'Kein Interesse',
      variant: 'no-interest' as const,
      color: 'bg-neutral-500'
    },
    {
      status: 'N/A' as VisitStatus,
      icon: <Clock className="h-8 w-8" />,
      label: 'Nicht da',
      variant: 'not-home' as const,
      color: 'bg-red-600'
    }
  ], []);

  return (
    <div className="grid grid-cols-2 gap-4">
      {actions.map((action) => (
        <QuickActionButton
          key={action.status}
          icon={action.icon}
          label={action.label}
          onClick={() => {
            triggerHapticFeedback('light');
            onAction(action.status);
          }}
          variant={action.variant}
          loading={isLoading}
        />
      ))}
    </div>
  );
});

const OptimizedVisitTracker: React.FC<OptimizedVisitTrackerProps> = ({
  visitId,
  initialData,
  onComplete
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { addAddress, addHouse, addVisit, addDoor, getVisit, getHouseById, getAddressById } = useData();
  
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAppointmentDialog, setShowAppointmentDialog] = useState(false);
  const [showProductSelection, setShowProductSelection] = useState(false);

  // Memoized visit data
  const visitData = useMemo(() => {
    if (!visitId) return null;
    const visit = getVisit(visitId);
    if (!visit) return null;
    
    const house = getHouseById(visit.houseId);
    if (!house) return null;
    
    const address = getAddressById(house.addressId);
    if (!address) return null;
    
    return { visit, house, address };
  }, [visitId, getVisit, getHouseById, getAddressById]);

  // Swipe navigation
  const swipeGestures = useSwipeNavigation(currentStep, 2, setCurrentStep);

  // Optimized submit handler with useCallback
  const handleSubmit = useCallback(async (status: VisitStatus) => {
    if (!user || !initialData) {
      toast.error('Fehlende Daten');
      return;
    }

    setIsSubmitting(true);
    triggerHapticFeedback('medium');

    try {
      // Batch operations for better performance
      const address = addAddress({
        street: initialData.street,
        houseNumber: initialData.houseNumber,
        city: initialData.city,
        zipCode: initialData.zipCode,
      });

      const house = addHouse({
        addressId: address.id,
        houseNumber: initialData.houseNumber,
        type: initialData.houseType,
        latitude: 48.7758,
        longitude: 9.1829,
      });

      const visit = addVisit({
        houseId: house.id,
        timestamp: new Date().toISOString(),
        status,
      });

      const doorName = initialData.houseType === 'EFH' ? 'Haupteingang' : 'Wohnung 1';
      addDoor({
        visitId: visit.id,
        name: doorName,
        status,
      });

      // Optimistic UI updates
      const statusMessages = {
        'N/A': 'Nicht angetroffen gespeichert',
        'Angetroffen → Kein Interesse': 'Kein Interesse gespeichert',
        'Angetroffen → Sale': 'Verkauf gespeichert! 🎉',
        'Angetroffen → Termin': 'Termin vereinbart',
      };

      toast.success(statusMessages[status]);

      // Navigate based on status
      if (status === 'Angetroffen → Sale') {
        setShowProductSelection(true);
      } else {
        onComplete?.();
        navigate('/daily-view');
      }

    } catch (error) {
      console.error('Error creating visit:', error);
      toast.error('Fehler beim Speichern');
      triggerHapticFeedback('heavy');
    } finally {
      setIsSubmitting(false);
    }
  }, [user, initialData, addAddress, addHouse, addVisit, addDoor, onComplete, navigate]);

  // Handle appointment confirmation
  const handleAppointmentConfirm = useCallback(async (date: string, time: string) => {
    await handleSubmit('Angetroffen → Termin');
    setShowAppointmentDialog(false);
  }, [handleSubmit]);

  // Handle quick actions
  const handleQuickAction = useCallback((status: VisitStatus) => {
    if (status === 'Angetroffen → Termin') {
      setShowAppointmentDialog(true);
      return;
    }
    handleSubmit(status);
  }, [handleSubmit]);

  // Navigation handlers
  const handleNext = useCallback(() => {
    if (currentStep < 2) {
      setCurrentStep(currentStep + 1);
      triggerHapticFeedback('light');
    }
  }, [currentStep]);

  const handlePrevious = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      triggerHapticFeedback('light');
    } else {
      navigate(-1);
    }
  }, [currentStep, navigate]);

  // Show product selection if needed
  if (showProductSelection && visitData) {
    return (
      <Suspense fallback={<LoadingSpinner />}>
        <ProductSelection />
      </Suspense>
    );
  }

  // Main render
  return (
    <div 
      ref={swipeGestures.ref}
      className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4"
    >
      <div className="max-w-md mx-auto space-y-6">
        <StepIndicator currentStep={currentStep} totalSteps={2} />

        {/* Address Card */}
        {initialData && (
          <AddressCard
            street={initialData.street}
            houseNumber={initialData.houseNumber}
            city={initialData.city}
            zipCode={initialData.zipCode}
            houseType={initialData.houseType}
            step={currentStep}
            totalSteps={2}
          />
        )}

        {/* Step Content */}
        {currentStep === 1 && (
          <Card variant="glass" className="transform transition-all duration-300">
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold mb-4 text-center">
                Bereit für den Besuch?
              </h2>
              <p className="text-neutral-600 text-center mb-6">
                Wischen Sie nach links oder tippen Sie auf "Weiter" um fortzufahren.
              </p>
              <Button
                size="lg"
                fullWidth
                onClick={handleNext}
                className="h-14"
              >
                Besuch starten
                <ChevronRight className="ml-2 h-5 w-5" />
              </Button>
            </CardContent>
          </Card>
        )}

        {currentStep === 2 && (
          <Card variant="glass" className="transform transition-all duration-300">
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold mb-6 text-center">
                Was ist passiert?
              </h2>
              <QuickActionGrid
                onAction={handleQuickAction}
                isLoading={isSubmitting}
              />
            </CardContent>
          </Card>
        )}

        {/* Navigation */}
        <div className="flex space-x-3">
          <Button
            variant="outline"
            size="lg"
            onClick={handlePrevious}
            disabled={isSubmitting}
            className="flex-1"
          >
            <ChevronLeft className="mr-2 h-5 w-5" />
            {currentStep === 1 ? 'Zurück' : 'Vorheriger'}
          </Button>
          
          {currentStep === 1 && (
            <Button
              size="lg"
              onClick={handleNext}
              className="flex-1"
            >
              Weiter
              <ChevronRight className="ml-2 h-5 w-5" />
            </Button>
          )}
        </div>

        {/* Swipe Hint */}
        <div className="text-center text-sm text-neutral-500">
          💡 Tipp: Wischen Sie nach links/rechts zum Navigieren
        </div>
      </div>

      {/* Lazy loaded dialogs */}
      {showAppointmentDialog && (
        <Suspense fallback={<LoadingSpinner />}>
          <AppointmentDialog
            open={showAppointmentDialog}
            onClose={() => setShowAppointmentDialog(false)}
            onConfirm={handleAppointmentConfirm}
          />
        </Suspense>
      )}
    </div>
  );
};

export default React.memo(OptimizedVisitTracker);
