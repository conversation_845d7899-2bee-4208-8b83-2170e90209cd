import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useData } from '@/context/data';
import { HouseType } from '@/types';
import { toast } from 'sonner';
import { 
  getCityByPostalCode, 
  getPostalCodeSuggestions,
  getStreetSuggestions 
} from '@/services/address';
import { MapPin, Building, Home as HomeIcon, Hash } from 'lucide-react';
import AddressFormField from './AddressFormField';
import ImprovedAutocomplete from './ImprovedAutocomplete';
import EFHVisitTracker from '../visit/EFHVisitTracker';
import { QuickNACard } from '@/components/visit-tracking/QuickNAButton';

interface ModernAddressFormProps {
  houseType: HouseType;
}

const ModernAddressForm: React.FC<ModernAddressFormProps> = ({ houseType }) => {
  const navigate = useNavigate();
  const { addAddress, addHouse, addVisit, addresses } = useData();
  
  const [zipCode, setZipCode] = useState('');
  const [zipCodeSuggestions, setZipCodeSuggestions] = useState<string[]>([]);
  const [city, setCity] = useState('');
  const [street, setStreet] = useState('');
  const [streetSuggestions, setStreetSuggestions] = useState<string[]>([]);
  const [houseNumber, setHouseNumber] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentVisitId, setCurrentVisitId] = useState<string | null>(null);

  // Validation states
  const isZipCodeValid = zipCode.length === 5 && /^\d{5}$/.test(zipCode);
  const isCityValid = city.length > 2;
  const isStreetValid = street.length > 2;
  const isHouseNumberValid = houseNumber.length > 0;
  const isFormValid = isZipCodeValid && isCityValid && isStreetValid && isHouseNumberValid;

  // Update city when zip code changes
  useEffect(() => {
    if (zipCode.length === 5) {
      const matchedCity = getCityByPostalCode(zipCode);
      if (matchedCity) {
        setCity(matchedCity);
      }
    }
  }, [zipCode]);

  // Update street suggestions when city changes
  useEffect(() => {
    if (city) {
      setStreetSuggestions(getStreetSuggestions(city, street));
    }
  }, [city, street]);

  // Update zip code suggestions when typing
  useEffect(() => {
    if (zipCode.length >= 2) {
      setZipCodeSuggestions(getPostalCodeSuggestions(zipCode));
    } else {
      setZipCodeSuggestions([]);
    }
  }, [zipCode]);

  const handleZipCodeChange = (value: string) => {
    // Only allow numbers and limit to 5 digits
    const cleanValue = value.replace(/\D/g, '').slice(0, 5);
    setZipCode(cleanValue);
    if (cleanValue.length !== 5) {
      setCity('');
    }
  };

  const handleHouseNumberChange = (value: string) => {
    // Allow numbers, letters, and common separators
    const cleanValue = value.replace(/[^0-9a-zA-Z\-\/\s]/g, '').slice(0, 10);
    setHouseNumber(cleanValue);
  };

  const validateAndShowError = () => {
    if (!isZipCodeValid) {
      toast.error('Bitte geben Sie eine gültige 5-stellige Postleitzahl ein');
      return false;
    }
    if (!isCityValid) {
      toast.error('Bitte geben Sie eine gültige Stadt ein');
      return false;
    }
    if (!isStreetValid) {
      toast.error('Bitte geben Sie eine gültige Straße ein');
      return false;
    }
    if (!isHouseNumberValid) {
      toast.error('Bitte geben Sie eine Hausnummer ein');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateAndShowError()) return;
    
    setIsSubmitting(true);
    
    try {
      // Check if address already exists
      let addressId: string;
      const existingAddress = addresses.find(
        addr => addr.zipCode === zipCode && 
               addr.city.toLowerCase() === city.toLowerCase() && 
               addr.street.toLowerCase() === street.toLowerCase()
      );
      
      if (existingAddress) {
        addressId = existingAddress.id;
        toast.success('Adresse bereits vorhanden - weiter zur Besuchserfassung');
      } else {
        // Create new address
        const newAddress = addAddress({
          zipCode,
          city,
          street
        });
        addressId = newAddress.id;
        toast.success('Neue Adresse erfolgreich hinzugefügt');
      }
      
      // Create house
      const house = addHouse({
        addressId,
        houseNumber,
        type: houseType,
        // Mock coordinates - in real app would get from geocoding
        latitude: 48.1351 + (Math.random() * 0.01),
        longitude: 11.5820 + (Math.random() * 0.01),
      });
      
      if (houseType === 'EFH') {
        // For EFH: Create visit immediately and show tracker
        const visit = addVisit({
          houseId: house.id,
          timestamp: new Date().toISOString(),
          status: 'N/A'
        });
        setCurrentVisitId(visit.id);
        toast.success('EFH-Besuch erstellt - Status erfassen');
      } else {
        // For MFH: Navigate to MFH manager
        navigate(`/mfh/${house.id}`);
      }
    } catch (error) {
      toast.error('Fehler beim Speichern der Adresse');
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNewEntry = () => {
    setCurrentVisitId(null);
    setZipCode('');
    setCity('');
    setStreet('');
    setHouseNumber('');
    toast.success('Bereit für neue Adresseingabe');
  };

  // Show EFH Visit Tracker if visit was created
  if (currentVisitId && houseType === 'EFH') {
    return (
      <div className="space-y-0 w-full">
        <EFHVisitTracker visitId={currentVisitId} />
        <Button
          onClick={handleNewEntry}
          variant="outline"
          className="w-full h-12 text-lg font-semibold rounded-none border-2 border-gray-300 hover:border-red-500 hover:text-red-600"
        >
          Neue Adresse eingeben
        </Button>
      </div>
    );
  }

  return (
    <Card className="w-full bg-white/95 backdrop-blur-sm border-0 shadow-none rounded-none overflow-hidden animate-fade-in m-0">
      <CardHeader className="text-center pb-6 pt-8 bg-gradient-to-b from-red-50/50 to-transparent m-0">
        <div className="flex items-center justify-center mb-4">
          {houseType === 'EFH' ? (
            <HomeIcon className="h-8 w-8 text-red-600" />
          ) : (
            <Building className="h-8 w-8 text-red-600" />
          )}
        </div>
        <CardTitle className="text-2xl font-bold text-gray-800">
          {houseType === 'EFH' ? 'Einfamilienhaus' : 'Mehrfamilienhaus'}
        </CardTitle>
        <p className="text-gray-600 mt-2">Adresse für den Besuch eingeben</p>
      </CardHeader>
      
      <CardContent className="px-8 pt-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <ImprovedAutocomplete
              id="zipCode"
              label="Postleitzahl"
              value={zipCode}
              onChange={handleZipCodeChange}
              suggestions={zipCodeSuggestions}
              placeholder="12345"
              required
              icon={MapPin}
              isValid={isZipCodeValid}
              maxSuggestions={8}
            />
            
            <AddressFormField
              id="city"
              label="Stadt"
              value={city}
              onChange={setCity}
              placeholder="Stadtname"
              required
              icon={Building}
              isValid={isCityValid}
              disabled={!isZipCodeValid}
              className={!isZipCodeValid ? "bg-gray-50" : ""}
            />
          </div>

          <ImprovedAutocomplete
            id="street"
            label="Straße"
            value={street}
            onChange={setStreet}
            suggestions={streetSuggestions}
            placeholder="Straßenname"
            required
            icon={MapPin}
            isValid={isStreetValid}
            maxSuggestions={12}
          />

          <AddressFormField
            id="houseNumber"
            label="Hausnummer"
            value={houseNumber}
            onChange={handleHouseNumberChange}
            placeholder="123a"
            required
            icon={Hash}
            isValid={isHouseNumberValid}
            maxLength={10}
          />
          
          {/* Quick N/A Option for EFH */}
          {houseType === 'EFH' && isFormValid && (
            <QuickNACard
              addressData={{
                zipCode,
                city,
                street,
                houseNumber,
                houseType,
                latitude: 48.1351 + (Math.random() * 0.01),
                longitude: 11.5820 + (Math.random() * 0.01),
              }}
              isVisible={true}
            />
          )}

          <CardFooter className="px-0 pt-6">
            <Button
              type="submit"
              className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 rounded-xl transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
              disabled={isSubmitting || !isFormValid}
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"></div>
                  Speichern...
                </div>
              ) : (
                `${houseType === 'EFH' ? 'Besuch erstellen' : 'Wohnungen verwalten'}`
              )}
            </Button>
          </CardFooter>
        </form>
      </CardContent>
    </Card>
  );
};

export default ModernAddressForm;
