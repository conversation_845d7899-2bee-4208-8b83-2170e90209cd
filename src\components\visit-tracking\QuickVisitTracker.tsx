import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useData } from '@/context/data';
import { useAuth } from '@/context/auth/AuthContext';
import { VisitStatus, HouseType } from '@/types';
import { toast } from 'sonner';
import { Card, CardContent, CardHeader, CardTitle, AddressCard } from '@/design-system/components/Card';
import { Button, StatusButton, QuickActionButton } from '@/design-system/components/Button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MapPin, Home, Building, Calendar, X, DollarSign, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface QuickVisitTrackerProps {
  initialAddress?: {
    street: string;
    houseNumber: string;
    city: string;
    zipCode: string;
  };
  onComplete?: () => void;
}

interface VisitForm {
  street: string;
  houseNumber: string;
  city: string;
  zipCode: string;
  houseType: HouseType;
  doorName: string;
  status: VisitStatus;
}

const QuickVisitTracker: React.FC<QuickVisitTrackerProps> = ({
  initialAddress,
  onComplete
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { addAddress, addHouse, addVisit, addDoor } = useData();
  
  const [step, setStep] = useState<1 | 2>(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAppointmentDialog, setShowAppointmentDialog] = useState(false);
  
  const [form, setForm] = useState<VisitForm>({
    street: initialAddress?.street || '',
    houseNumber: initialAddress?.houseNumber || '',
    city: initialAddress?.city || 'Stuttgart',
    zipCode: initialAddress?.zipCode || '70173',
    houseType: 'EFH',
    doorName: '',
    status: 'N/A'
  });

  // Auto-generate door name based on house type
  useEffect(() => {
    if (form.houseType === 'EFH') {
      setForm(prev => ({ ...prev, doorName: 'Haupteingang' }));
    } else {
      setForm(prev => ({ ...prev, doorName: '' }));
    }
  }, [form.houseType]);

  const handleFormChange = (field: keyof VisitForm, value: string) => {
    setForm(prev => ({ ...prev, [field]: value }));
  };

  const handleStep1Submit = () => {
    if (!form.street || !form.houseNumber || !form.city || !form.zipCode) {
      toast.error('Bitte füllen Sie alle Adressfelder aus');
      return;
    }
    setStep(2);
  };

  const handleQuickStatus = async (status: VisitStatus) => {
    if (status === 'Angetroffen → Termin') {
      setShowAppointmentDialog(true);
      return;
    }

    await handleSubmit(status);
  };

  const handleSubmit = async (status: VisitStatus = form.status) => {
    if (!user) {
      toast.error('Benutzer nicht angemeldet');
      return;
    }

    setIsSubmitting(true);

    try {
      // Create address
      const address = addAddress({
        street: form.street,
        houseNumber: form.houseNumber,
        city: form.city,
        zipCode: form.zipCode,
      });

      // Create house
      const house = addHouse({
        addressId: address.id,
        houseNumber: form.houseNumber,
        type: form.houseType,
        latitude: 48.7758, // Default Stuttgart coordinates
        longitude: 9.1829,
      });

      // Create visit
      const visit = addVisit({
        houseId: house.id,
        timestamp: new Date().toISOString(),
        status,
      });

      // Create door with smart default name
      const doorName = form.doorName || (form.houseType === 'EFH' ? 'Haupteingang' : 'Wohnung 1');
      addDoor({
        visitId: visit.id,
        name: doorName,
        status,
      });

      // Success feedback
      const statusMessages = {
        'N/A': 'Besuch als "Nicht angetroffen" gespeichert',
        'Angetroffen → Kein Interesse': 'Besuch als "Kein Interesse" gespeichert',
        'Angetroffen → Sale': 'Verkauf erfolgreich gespeichert!',
        'Angetroffen → Termin': 'Termin erfolgreich vereinbart',
      };

      toast.success(statusMessages[status]);

      // Navigate based on status
      if (status === 'Angetroffen → Sale') {
        navigate(`/products/${visit.id}`);
      } else {
        onComplete?.();
        navigate('/daily-view');
      }

    } catch (error) {
      console.error('Error creating visit:', error);
      toast.error('Fehler beim Speichern des Besuchs');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    if (step === 2) {
      setStep(1);
    } else {
      navigate(-1);
    }
  };

  // Step 1: Address & House Type
  if (step === 1) {
    return (
      <div className="space-y-6 max-w-md mx-auto p-4">
        <Card variant="glass">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <MapPin className="h-5 w-5 text-blue-600" />
              <span>Adresse eingeben</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-3">
              <div className="col-span-2">
                <Label htmlFor="street">Straße</Label>
                <Input
                  id="street"
                  value={form.street}
                  onChange={(e) => handleFormChange('street', e.target.value)}
                  placeholder="Musterstraße"
                  className="h-12"
                />
              </div>
              <div>
                <Label htmlFor="houseNumber">Nr.</Label>
                <Input
                  id="houseNumber"
                  value={form.houseNumber}
                  onChange={(e) => handleFormChange('houseNumber', e.target.value)}
                  placeholder="1"
                  className="h-12"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <Label htmlFor="zipCode">PLZ</Label>
                <Input
                  id="zipCode"
                  value={form.zipCode}
                  onChange={(e) => handleFormChange('zipCode', e.target.value)}
                  placeholder="70173"
                  className="h-12"
                />
              </div>
              <div>
                <Label htmlFor="city">Stadt</Label>
                <Input
                  id="city"
                  value={form.city}
                  onChange={(e) => handleFormChange('city', e.target.value)}
                  placeholder="Stuttgart"
                  className="h-12"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="houseType">Haustyp</Label>
              <Select
                value={form.houseType}
                onValueChange={(value: HouseType) => handleFormChange('houseType', value)}
              >
                <SelectTrigger className="h-12">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="EFH">
                    <div className="flex items-center space-x-2">
                      <Home className="h-4 w-4" />
                      <span>Einfamilienhaus (EFH)</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="MFH">
                    <div className="flex items-center space-x-2">
                      <Building className="h-4 w-4" />
                      <span>Mehrfamilienhaus (MFH)</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {form.houseType === 'MFH' && (
              <div>
                <Label htmlFor="doorName">Wohnung/Tür</Label>
                <Input
                  id="doorName"
                  value={form.doorName}
                  onChange={(e) => handleFormChange('doorName', e.target.value)}
                  placeholder="z.B. Wohnung 1, Erdgeschoss links"
                  className="h-12"
                />
              </div>
            )}
          </CardContent>
        </Card>

        <div className="flex space-x-3">
          <Button
            variant="outline"
            size="lg"
            onClick={handleBack}
            className="flex-1"
          >
            Zurück
          </Button>
          <Button
            size="lg"
            onClick={handleStep1Submit}
            className="flex-1"
          >
            Weiter
          </Button>
        </div>
      </div>
    );
  }

  // Step 2: Quick Status Selection
  return (
    <div className="space-y-6 max-w-md mx-auto p-4">
      <AddressCard
        street={form.street}
        houseNumber={form.houseNumber}
        city={form.city}
        zipCode={form.zipCode}
        houseType={form.houseType}
        step={2}
        totalSteps={2}
      />

      <Card variant="glass">
        <CardHeader>
          <CardTitle>Was ist passiert?</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <QuickActionButton
              icon={<DollarSign className="h-8 w-8" />}
              label="Verkauf!"
              onClick={() => handleQuickStatus('Angetroffen → Sale')}
              variant="sale"
              loading={isSubmitting}
            />
            <QuickActionButton
              icon={<Calendar className="h-8 w-8" />}
              label="Termin"
              onClick={() => handleQuickStatus('Angetroffen → Termin')}
              variant="appointment"
              loading={isSubmitting}
            />
            <QuickActionButton
              icon={<X className="h-8 w-8" />}
              label="Kein Interesse"
              onClick={() => handleQuickStatus('Angetroffen → Kein Interesse')}
              variant="no-interest"
              loading={isSubmitting}
            />
            <QuickActionButton
              icon={<Clock className="h-8 w-8" />}
              label="Nicht da"
              onClick={() => handleQuickStatus('N/A')}
              variant="not-home"
              loading={isSubmitting}
            />
          </div>
        </CardContent>
      </Card>

      <div className="flex space-x-3">
        <Button
          variant="outline"
          size="lg"
          onClick={handleBack}
          className="flex-1"
          disabled={isSubmitting}
        >
          Zurück
        </Button>
      </div>

      {/* Appointment Dialog would go here */}
    </div>
  );
};

export default QuickVisitTracker;
