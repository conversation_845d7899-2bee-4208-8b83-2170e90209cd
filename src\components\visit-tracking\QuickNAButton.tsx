import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/design-system/components/Button';
import { useData } from '@/context/data';
import { useAuth } from '@/context/auth';
import { toast } from 'sonner';
import { UserX, Zap } from 'lucide-react';
import { cn } from '@/lib/utils';
import { animations } from '@/design-system/animations';
import { triggerHapticFeedback } from '@/hooks/useSwipeGestures';

interface QuickNAButtonProps {
  addressData: {
    zipCode: string;
    city: string;
    street: string;
    houseNumber: string;
    houseType: 'EFH' | 'MFH';
    latitude: number;
    longitude: number;
  };
  className?: string;
}

export const QuickNAButton: React.FC<QuickNAButtonProps> = ({ 
  addressData, 
  className 
}) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { addAddress, addHouse, addVisit, addDoor } = useData();
  const [isProcessing, setIsProcessing] = useState(false);

  const handleQuickNA = async () => {
    if (!user) {
      toast.error('Benutzer nicht angemeldet');
      return;
    }

    setIsProcessing(true);
    triggerHapticFeedback('medium');

    try {
      // 1. Create address
      const address = addAddress({
        zipCode: addressData.zipCode,
        city: addressData.city,
        street: addressData.street,
      });

      // 2. Create house
      const house = addHouse({
        addressId: address.id,
        houseNumber: addressData.houseNumber,
        type: addressData.houseType,
        latitude: addressData.latitude,
        longitude: addressData.longitude,
      });

      // 3. Create visit with N/A status
      const visit = addVisit({
        houseId: house.id,
        timestamp: new Date().toISOString(),
        status: 'N/A'
      });

      // 4. Create door for EFH
      if (addressData.houseType === 'EFH') {
        addDoor({
          visitId: visit.id,
          name: 'Haupteingang',
          status: 'N/A'
        });
      }

      // Success feedback
      toast.success('⚡ Nicht angetroffen - Schnell gespeichert!');
      triggerHapticFeedback('light');

      // Quick redirect to home
      setTimeout(() => {
        navigate('/');
      }, 800);

    } catch (error) {
      console.error('Error in Quick N/A:', error);
      toast.error('Fehler beim Speichern');
      triggerHapticFeedback('heavy');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Button
      onClick={handleQuickNA}
      loading={isProcessing}
      disabled={isProcessing}
      variant="outline"
      size="lg"
      fullWidth
      leftIcon={<UserX className="h-5 w-5" />}
      rightIcon={<Zap className="h-4 w-4" />}
      className={cn(
        'h-14 border-2 border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400',
        'transition-all duration-250 active:scale-95',
        animations.classes.hoverScale,
        className
      )}
    >
      <span className="flex items-center gap-2">
        <span className="font-semibold">Quick N/A</span>
        <span className="text-sm opacity-75">Nicht angetroffen</span>
      </span>
    </Button>
  );
};

// Floating Quick N/A Button for existing addresses
export const FloatingQuickNAButton: React.FC<{
  onQuickNA: () => void;
  isVisible: boolean;
}> = ({ onQuickNA, isVisible }) => {
  return (
    <div
      className={cn(
        'fixed bottom-20 right-4 z-50 transition-all duration-300',
        isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10 pointer-events-none'
      )}
    >
      <Button
        onClick={onQuickNA}
        variant="error"
        size="lg"
        className={cn(
          'h-14 w-14 rounded-full shadow-lg hover:shadow-xl',
          'bg-red-500 hover:bg-red-600 text-white',
          animations.classes.hoverScale
        )}
      >
        <UserX className="h-6 w-6" />
      </Button>
      <div className="absolute -top-8 right-0 bg-black/80 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
        Quick N/A
      </div>
    </div>
  );
};

// Quick N/A Card for address form
export const QuickNACard: React.FC<{
  addressData: QuickNAButtonProps['addressData'];
  isVisible: boolean;
}> = ({ addressData, isVisible }) => {
  if (!isVisible) return null;

  return (
    <div className={cn(
      'mt-4 p-4 bg-red-50 border-2 border-red-200 rounded-xl',
      animations.classes.slideAndFade
    )}>
      <div className="flex items-center gap-3 mb-3">
        <div className="p-2 bg-red-100 rounded-lg">
          <Zap className="h-5 w-5 text-red-600" />
        </div>
        <div>
          <h3 className="font-semibold text-red-800">Schnell-Tracking</h3>
          <p className="text-sm text-red-600">Für häufige "Nicht angetroffen" Fälle</p>
        </div>
      </div>
      
      <QuickNAButton 
        addressData={addressData}
        className="mt-3"
      />
      
      <p className="text-xs text-red-500 mt-2 text-center">
        💡 Speichert automatisch und kehrt zur Startseite zurück
      </p>
    </div>
  );
};

export default QuickNAButton;
